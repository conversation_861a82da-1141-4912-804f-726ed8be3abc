{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue?vue&type=template&id=47d2a8be&scoped=true&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue", "mtime": 1754358523822}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1667694382645}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}