# 工单保险公司筛选功能 - SQL修改验证

## 问题分析
前端调用的是 `/insurance/work-order/page2` 接口，对应后端的 `getWorkOrder2Page2` 方法，该方法使用的是 `selectWorkOrderPage` SQL查询，而不是 `selectPage` 方法。

## 修改内容

### 修改的文件
1. `yudao-module-insurance/yudao-module-insurance-biz/src/main/resources/mapper/workorder2/WorkOrder2Mapper.xml`
2. `yudao-module-insurance/yudao-module-insurance-biz/src/main/java/cn/iocoder/yudao/module/insurance/controller/admin/workorder2/vo/WorkOrder2ExportReqVO.java`

### 具体修改

#### 1. 分页查询支持
在 `selectWorkOrderPage` 查询的 WHERE 条件中添加了对 `companyId` 参数的处理：

```xml
<if test="reqVO.companyId != null"> and ic.id = #{reqVO.companyId}</if>
```

#### 2. 导出功能支持
- 在 `WorkOrder2ExportReqVO` 中添加了 `companyId` 字段
- 在 `selectList2` 查询的 WHERE 条件中添加了对 `companyId` 参数的处理

### 修改位置
- 文件：`WorkOrder2Mapper.xml`
  - 方法：`selectWorkOrderPage` - 第148行（新增）
  - 方法：`selectList2` - 第351行（新增）
- 文件：`WorkOrder2ExportReqVO.java` - 第62-63行（新增字段）

### SQL逻辑说明
1. 该SQL已经通过 `LEFT JOIN insurance_company as ic on iac.company_id = ic.id` 关联了保险公司表
2. 新增的条件 `ic.id = #{reqVO.companyId}` 会根据传入的保险公司ID进行筛选
3. 使用 `<if test="reqVO.companyId != null">` 确保只有当companyId不为空时才添加此条件

### 完整的关联逻辑
```sql
from insurance_work_order2 as iwo2
left join insurance_work_order_pdf as iwop on iwop.order_id = iwo2.id
left join insurance_area_company as iac on iwo2.area_id = iac.area_id
LEFT JOIN insurance_company as ic on iac.company_id = ic.id
```

这个关联逻辑通过以下步骤建立工单与保险公司的关系：
1. `iwo2.area_id = iac.area_id` - 工单关联到区域公司关系表
2. `iac.company_id = ic.id` - 区域公司关系表关联到保险公司表

## 验证要点
- [ ] 前端传递 `companyId` 参数时，后端能正确接收
- [ ] SQL查询能根据 `companyId` 正确筛选工单
- [ ] 不传递 `companyId` 时，查询结果不受影响
- [ ] 与其他筛选条件组合使用正常

## 相关代码路径
- 前端API调用：`yudao-ui-admin/src/api/insurance/workOrder.js` - `getWorkOrderPage`
- 后端Controller：`WorkOrder2Controller.java` - `getWorkOrder2Page2`
- 后端Service：`WorkOrder2ServiceImpl.java` - `getWorkOrder2Page2`
- 后端Mapper：`WorkOrder2Mapper.java` - `selectWorkOrderPage`
- SQL实现：`WorkOrder2Mapper.xml` - `selectWorkOrderPage`

## 测试建议
1. 选择一个已知的保险公司ID进行筛选测试
2. 验证筛选结果只包含该保险公司的工单
3. 测试与其他筛选条件的组合使用
4. 验证清空保险公司选择后能显示所有工单
