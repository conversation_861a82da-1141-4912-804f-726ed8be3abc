{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue?vue&type=style&index=0&id=47d2a8be&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue", "mtime": 1754359337665}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\css-loader@3.6.0_webpack@4.46.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1667694382297}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1667694382639}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1667694382634}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\sass-loader@10.1.1_sass@1.32.13_webpack@4.46.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1667694316537}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIC5kcmF3ZXItZm9vdGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIHBhZGRpbmc6IDAgNTBweCAyMHB4Ow0KICAgIC5lbC1idXR0b24gew0KICAgICAgZmxleDogMQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2nCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/insurance/workOrder", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工作栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"90px\">\r\n      <el-form-item label=\"保险类型\">\r\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择保险类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" v-if=\"(dict.value == '1' && checkPermi(['insurance:old-people-accident-insurance:query'])) || ((dict.value == '7' || dict.value == '8' || dict.value == '9') && checkPermi(['insurance:disabled-people-insurance:query']))\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"医院名称\">\r\n        <el-select v-model=\"queryParams.hospitalName\" placeholder=\"请选择医院\" clearable>\r\n          <el-option\r\n            v-for=\"item in hospitalNames\"\r\n            :key=\"item\"\r\n            :label=\"item\"\r\n            :value=\"item\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"保险公司\" v-if=\"checkPermi(['insurance:company:query'])\">\r\n        <CompanySelect v-model=\"queryParams.companyId\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"长者名称\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入长者名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证号\" prop=\"idCardNumber\">\r\n        <el-input v-model=\"queryParams.idCardNumber\" placeholder=\"请输入身份证号\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"mobilePhoneNumber\">\r\n        <el-input v-model=\"queryParams.mobilePhoneNumber\" placeholder=\"请输入联系方式\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"理赔时间\">\r\n        <el-date-picker v-model=\"dateRangeCompensatingDate\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"工单类型\">\r\n        <el-select v-model=\"queryParams.treatmentSerialNumberType\" placeholder=\"请选择工单类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TREATMENT_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"票据完整度\">\r\n        <el-select v-model=\"queryParams.completeStatus\" placeholder=\"请选择完整度\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRangeCreateTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n                   v-hasPermi=\"['insurance:work-order:create']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:work-order:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleImport\"\r\n                   v-hasPermi=\"['insurance:work-order:settlement-import']\">已理赔工单导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleClaimAmountImport\"\r\n                   v-hasPermi=\"['insurance:work-order:claim-import']\">理赔金额导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" size=\"mini\" icon=\"el-icon-upload2\"\r\n                   @click=\"handleWandaImport\" :loading=\"wandaImportLoading\"\r\n                   v-hasPermi=\"['insurance:work-order:import']\">万达医疗数据更新</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-document\" size=\"mini\"\r\n                   @click=\"handleBatchOperationLog\"\r\n                   v-hasPermi=\"['insurance:work-order:query']\">批量操作日志</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\"\r\n                   @click=\"handleBatchReject\"\r\n                   v-hasPermi=\"['insurance:work-order:batch-reject']\">批量拒绝旧工单</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\">\r\n      <el-table-column label=\"编号\" align=\"center\" type=\"index\" width=\"50px\"/>\r\n      <el-table-column label=\"保险类型\" align=\"center\" prop=\"type\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TYPE\" :value=\"scope.row.type\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"工单类型\" align=\"center\" prop=\"treatmentSerialNumberType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TREATMENT_TYPE\" :value=\"scope.row.treatmentSerialNumberType\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column v-if=\"checkPermi(['insurance:work-order:show-company'])\" label=\"负责公司\" align=\"center\" prop=\"companyName\" />\r\n      <el-table-column label=\"长者名称\" align=\"center\" prop=\"desensitizedName\" />\r\n      <el-table-column label=\"身份证号\" align=\"center\" prop=\"desensitizedIdCardNumber\" />\r\n      <el-table-column label=\"联系方式\" align=\"center\" prop=\"desensitizedMobilePhoneNumber\" />\r\n      <el-table-column label=\"医院\" align=\"center\" prop=\"hospitalName\" />\r\n      <el-table-column label=\"就诊时间\" align=\"center\" prop=\"treatmentDatetime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.treatmentDatetime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"建议理赔金额\" align=\"center\" width=\"100\" prop=\"suggestCompensatingMoney\" />\r\n      <el-table-column label=\"赔付金额\" align=\"center\" prop=\"actualMoney\" />\r\n      <el-table-column label=\"票据完整度\" align=\"center\" prop=\"completeStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS\" :value=\"scope.row.completeStatus\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"险种\" align=\"center\" prop=\"completeStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TYPE\" :value=\"scope.row.type\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"授权状态\" align=\"center\" prop=\"authStatus\" />\r\n      <el-table-column label=\"理赔申请\" align=\"center\" prop=\"supplementaryFileRecordId\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.supplementaryFileRecordId == null\">否</el-tag>\r\n          <el-tag v-else type=\"info\">是</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column fixed=\"right\" label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"300px\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleEcard(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:cert'])\">电子证照</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleOrder(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:ticket'])\">电子保单</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleHouseHold(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:house'])\">电子户口</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleBankCard(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:bankcard'])\">银行账号</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handlerPersonInfo(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">联系方式</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleDisablePerson(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">残疾人证</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleRecord(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:medical']) && scope.row.status > 1\">就诊证明</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"if(!scope.row.supplementaryFileRecordId) { handleDetail(scope.row); } else { handleDetail2(scope.row); }\"\r\n                    v-if=\"checkPermi(['insurance:work-order:query'])\">详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleTake(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:take']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">接单</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleReject(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:reject']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">拒绝</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleDelay(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:delay']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">延后</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleHospitalCheck(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:hospital-check']) && scope.row.status === 1 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">盖章</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleProcess(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:process']) && scope.row.status === 2 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">处理</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleVisit(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:visit']) && scope.row.status === 3 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">回访</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleReturn(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:return']) && (scope.row.status === 2 || scope.row.status === 3 || scope.row.status === 6) && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">回退</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleSupplementary(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">补充资料</el-button>\r\n          <el-dropdown  @command=\"(command) => handleCommand(command, scope.$index, scope.row)\"\r\n                        v-if=\"queryParams.status != null\"\r\n                        v-hasPermi=\"['insurance:work-order:update', 'insurance:work-order:delete']\">\r\n                <span class=\"el-dropdown-link\">\r\n                  <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\r\n                </span>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"handleDelete\" size=\"mini\" type=\"text\" icon=\"el-icon-delete\"\r\n                                v-hasPermi=\"['insurance:work-order:delete']\">删除</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleCreatePdf\" size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n                                v-hasPermi=\"['insurance:work-order:update']\">创建未签章pdf</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <el-drawer :title=\"detailTitle\" :visible.sync=\"detailOpen\" direction=\"rtl\" size=\"60%\">\r\n      <WorkOrderDetail ref=\"workOrderDetail\" :opType=\"opType\" v-if=\"detailOpen\" :id=\"detailId\" />\r\n      <div class=\"drawer-footer\" v-if=\"detailTitle !== '查看详情'\">\r\n        <el-button type=\"primary\" @click=\"doAction\" :loading=\"confirmBtnLoading\">{{confirmBtnText}}</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer title=\"电子签章\" :visible.sync=\"esignOpen\" direction=\"rtl\" size=\"90%\">\r\n      <esign />\r\n      <div class=\"drawer-footer\">\r\n        <el-button type=\"primary\" @click=\"doAction\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog title=\"电子证照\" :visible.sync=\"ecardOpen\" width=\"500px\">\r\n      <ecard :idNum=\"ecard.idNum\" :name=\"ecard.name\" />\r\n    </el-dialog>\r\n    <el-drawer title=\"保单详情\" :visible.sync=\"orderOpen\" direction=\"rtl\" size=\"90%\">\r\n      <order :idNum=\"idNum\"/>\r\n    </el-drawer>\r\n    <el-drawer title=\"残疾人证\" :visible.sync=\"disablePerson.open\" direction=\"rtl\" size=\"90%\">\r\n      <disablePerson :idNum=\"disablePerson.idNum\"/>\r\n    </el-drawer>\r\n    <el-dialog title=\"电子户口\" :visible.sync=\"houseHoldOpen\" width=\"550px\">\r\n      <household :idNum=\"household.idNum\" />\r\n    </el-dialog>\r\n    <el-dialog title=\"银行账号\" :visible.sync=\"bankAccount.bankAccountOpen\" width=\"550px\">\r\n      <bankCard :idCard=\"bankAccount.idNum\" />\r\n    </el-dialog>\r\n    <el-dialog title=\"多来源信息\" :visible.sync=\"personInfo.open\" width=\"550px\">\r\n      <personInfo :idCard=\"personInfo.idNum\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\r\n      <el-upload ref=\"upload\" :limit=\"1\" accept=\".xlsx, .xls\" :headers=\"upload.headers\"\r\n        :action=\"upload.url\" :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\" :on-success=\"handleFileSuccess\" :auto-upload=\"false\" drag>\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"补充资料\" :visible.sync=\"supplementary.open\" width=\"800px\" append-to-body>\r\n      <el-tabs v-model=\"supplementary.activeTab\">\r\n        <el-tab-pane v-for=\"(urls, type) in supplementary.files\"\r\n                     :key=\"type\"\r\n                     :label=\"getSupplementaryTypeLabel(type)\"\r\n                     :name=\"type\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\" v-for=\"(url, index) in urls\" :key=\"index\">\r\n              <el-image\r\n                style=\"width: 100%; height: 200px\"\r\n                :src=\"url\"\r\n                :preview-src-list=\"urls\">\r\n              </el-image>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog :title=\"'补充资料详情'\" :visible.sync=\"detailOpen2\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"居民\">{{ detailForm.residentName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"状态\">\r\n          <dict-tag :type=\"DICT_TYPE.SUPPLEMENTARY_RECORD_STATUS\" :value=\"detailForm.status\"/>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"户籍地址\">{{ detailForm.hjAddress }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"手机号\">{{ detailForm.phone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"实际理赔金额\" v-if=\"detailForm.actualMoney\">\r\n          {{ detailForm.actualMoney }}元\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <el-divider content-position=\"left\">补充资料</el-divider>\r\n      <el-tabs v-model=\"activeTab\">\r\n        <el-tab-pane\r\n          v-for=\"(urls, type) in fileUrlMap\"\r\n          :key=\"type\"\r\n          :label=\"getSupplementaryTypeLabel(type)\"\r\n          :name=\"type\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\" v-for=\"(url, index) in urls\" :key=\"index\">\r\n              <el-image\r\n                style=\"width: 100%; height: 200px\"\r\n                :src=\"url\"\r\n                :preview-src-list=\"urls\">\r\n              </el-image>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n\r\n    <!-- 批量操作日志抽屉 -->\r\n    <el-drawer\r\n      title=\"批量操作日志\"\r\n      :visible.sync=\"batchLogDrawer.visible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleBatchLogDrawerClose\">\r\n\r\n      <!-- 搜索条件 -->\r\n      <el-form :model=\"batchLogDrawer.queryParams\" ref=\"batchLogQueryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\" style=\"margin-bottom: 20px;\">\r\n        <el-form-item label=\"操作类型\">\r\n          <el-select v-model=\"batchLogDrawer.queryParams.operationType\" placeholder=\"请选择操作类型\" clearable>\r\n            <el-option label=\"批量拒绝\" value=\"BATCH_REJECT\"></el-option>\r\n            <el-option label=\"批量恢复\" value=\"BATCH_RECOVER\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作状态\">\r\n          <el-select v-model=\"batchLogDrawer.queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n            <el-option label=\"执行中\" value=\"RUNNING\"></el-option>\r\n            <el-option label=\"已完成\" value=\"COMPLETED\"></el-option>\r\n            <el-option label=\"执行失败\" value=\"FAILED\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作员\">\r\n          <el-input v-model=\"batchLogDrawer.queryParams.operatorName\" placeholder=\"请输入操作员姓名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作时间\">\r\n          <el-date-picker\r\n            v-model=\"batchLogDrawer.dateRange\"\r\n            style=\"width: 240px\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"-\"\r\n            start-placeholder=\"开始时间\"\r\n            end-placeholder=\"结束时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getBatchLogList\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetBatchLogQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 批量操作日志表格 -->\r\n      <el-table v-loading=\"batchLogDrawer.loading\" :data=\"batchLogDrawer.list\" style=\"width: 100%\">\r\n        <el-table-column label=\"批次号\" align=\"center\" prop=\"batchId\" width=\"180\" show-overflow-tooltip></el-table-column>\r\n        <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationTypeDisplay\" width=\"100\"></el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"statusDisplay\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getBatchLogStatusType(scope.row.status)\">{{ scope.row.statusDisplay }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"处理数量\" align=\"center\" prop=\"processedCount\" width=\"80\"></el-table-column>\r\n        <el-table-column label=\"操作员\" align=\"center\" prop=\"operatorName\" width=\"100\"></el-table-column>\r\n        <el-table-column label=\"开始时间\" align=\"center\" prop=\"startTime\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"结束时间\" align=\"center\" prop=\"endTime\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.endTime ? parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"执行时长\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDuration(scope.row.duration) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remarks\" show-overflow-tooltip></el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <pagination\r\n        v-show=\"batchLogDrawer.total > 0\"\r\n        :total=\"batchLogDrawer.total\"\r\n        :page.sync=\"batchLogDrawer.queryParams.pageNo\"\r\n        :limit.sync=\"batchLogDrawer.queryParams.pageSize\"\r\n        @pagination=\"getBatchLogList\"\r\n        style=\"margin-top: 20px;\" />\r\n    </el-drawer>\r\n\r\n    <!-- 批量拒绝模态框 -->\r\n    <el-dialog\r\n      title=\"批量拒绝历史待接单工单\"\r\n      :visible.sync=\"batchRejectDialog.visible\"\r\n      width=\"500px\"\r\n      :before-close=\"handleBatchRejectDialogClose\">\r\n\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <p style=\"color: #606266; line-height: 1.6;\">\r\n          此操作将批量拒绝指定日期及之前的所有待接单工单。被拒绝的工单状态将变更为\"行政拒绝\"，\r\n          此操作可通过批量操作日志进行恢复。请谨慎操作。\r\n        </p>\r\n      </div>\r\n\r\n      <el-form :model=\"batchRejectDialog.form\" ref=\"batchRejectForm\" label-width=\"120px\">\r\n        <el-form-item label=\"截止日期\" prop=\"cutoffDate\"\r\n                      :rules=\"[{ required: true, message: '请选择截止日期', trigger: 'change' }]\">\r\n          <el-date-picker\r\n            v-model=\"batchRejectDialog.form.cutoffDate\"\r\n            type=\"date\"\r\n            placeholder=\"选择截止日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 100%\">\r\n          </el-date-picker>\r\n          <div style=\"font-size: 12px; color: #909399; margin-top: 5px;\">\r\n            将拒绝此日期及之前的所有待接单工单\r\n          </div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleBatchRejectDialogClose\">取 消</el-button>\r\n        <el-button type=\"danger\" @click=\"handleBatchRejectConfirm\" :loading=\"batchRejectDialog.loading\">执 行</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  createWorkOrder,\r\n  updateWorkOrder,\r\n  deleteWorkOrder,\r\n  getWorkOrder,\r\n  getWorkOrderPage,\r\n  exportWorkOrderExcel,\r\n  takeWorkOrder,\r\n  hospitalCheck,\r\n  process as process2,\r\n  visit,\r\n  reject,\r\n  delay,\r\n  returnWorkOrder,\r\n  getHospitalNames,\r\n  createPdf,\r\n  getBatchOperationLogPage,\r\n  batchRejectWorkOrders\r\n}\r\nfrom \"@/api/insurance/workOrder\";\r\nimport {getSupplementaryFileRecord} from \"@/api/insurance/supplementaryFileRecord\";\r\nimport ImageUpload from '@/components/ImageUpload';\r\nimport { checkPermi, checkRole } from \"@/utils/permission\";\r\nimport WorkOrderDetail from \"./detail\"\r\nimport ecard from '../components/ecard.vue';\r\nimport esign from '../components/esignature.vue';\r\nimport order from '../components/order.vue';\r\nimport disablePerson from '../components/disablePerson.vue';\r\nimport household from '../components/household.vue';\r\nimport bankCard from '../components/bankCard.vue';\r\nimport personInfo from '../components/personInfo.vue';\r\nimport CompanySelect from '../company/components/companySelect.vue';\r\nimport {getBaseHeader} from \"@/utils/request\";\r\nconst CONFIRM_TEXT = '确 定';\r\nexport default {\r\n  name: \"WorkOrder\",\r\n  components: {\r\n    ImageUpload,\r\n    WorkOrderDetail,\r\n    ecard,\r\n    esign,\r\n    order,\r\n    household,\r\n    bankCard,\r\n    personInfo,\r\n    disablePerson,\r\n    CompanySelect,\r\n  },\r\n  data() {\r\n\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工单列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      dateRangeCompensatingDate: [],\r\n      dateRangeCreateTime: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        type: null,\r\n        name: null,\r\n        idCardNumber: null,\r\n        mobilePhoneNumber: null,\r\n        treatmentSerialNumberType: this.canInitialFilter()? '1': null,\r\n        completeStatus: this.canInitialFilter()? '0': null,\r\n        hospitalName: null,\r\n        companyId: null,\r\n        types: [1, 7, 8, 9],\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        name: [{ required: true, message: \"长者名称不能为空\", trigger: \"blur\" }],\r\n        idCardNumber: [{ required: true, message: \"身份证号不能为空\", trigger: \"blur\" }],\r\n      },\r\n      detailId: undefined,\r\n      detailTitle: undefined,\r\n      detailOpen: false,\r\n      detailOpen2: false,\r\n      method: undefined,\r\n      ecardOpen: false,\r\n      esignOpen: false,\r\n      orderOpen: false,\r\n      disablePerson: {\r\n        open: false,\r\n        idNum: undefined,\r\n      },\r\n      ecard: {\r\n        idNum: undefined,\r\n        name: undefined\r\n      },\r\n      confirmBtnText: CONFIRM_TEXT,\r\n      confirmBtnLoading: false,\r\n      houseHoldOpen: false,\r\n      household: {\r\n        idNum: undefined,\r\n      },\r\n      opType: undefined,\r\n      idNum: undefined,\r\n      bankAccount: {\r\n        bankAccountOpen: false,\r\n        idNum: undefined\r\n      },\r\n      personInfo: {\r\n        open: false,\r\n        idNum: undefined\r\n      },\r\n      hospitalNames: [],\r\n      //已赔付工单导入\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: getBaseHeader(),\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-settlement-work-order',\r\n        claimAmountUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-claim-amount-work-order',\r\n        wandaImportUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-wanda-data'\r\n      },\r\n      supplementary: {\r\n        open: false,\r\n        activeTab: '',\r\n        files: {},\r\n      },\r\n      supplementaryTypeMap: {\r\n        MEDICAL_DIAGNOSIS_PROOF: '医疗诊断证明',\r\n        MEDICAL_FEE_INVOICE: '医疗费用发票',\r\n        DRUG_LIST: '用药清单',\r\n        MEDICAL_SETTLEMENT: '医保结算单',\r\n        OUTPATIENT_MEDICAL_RECORDS: '门诊病历(门诊)',\r\n        DISCHARGE_RECORD: '出院记录(住院)',\r\n        DISABILITY_CERTIFICATE: '残疾鉴定报告',\r\n        TRAFFIC_ACCIDENT_CERTIFICATE: '交通事故责任认定书'\r\n      },\r\n      wandaImportLoading: false,\r\n      detailForm: {},\r\n      fileUrlMap: {},\r\n      activeTab: '',\r\n      // 批量操作日志抽屉\r\n      batchLogDrawer: {\r\n        visible: false,\r\n        loading: false,\r\n        total: 0,\r\n        list: [],\r\n        dateRange: [],\r\n        queryParams: {\r\n          pageNo: 1,\r\n          pageSize: 10,\r\n          operationType: null,\r\n          status: null,\r\n          operatorName: null,\r\n          beginStartTime: null,\r\n          endStartTime: null\r\n        }\r\n      },\r\n      // 批量拒绝对话框\r\n      batchRejectDialog: {\r\n        visible: false,\r\n        loading: false,\r\n        form: {\r\n          cutoffDate: null\r\n        }\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    getHospitalNames().then(response => {\r\n      this.hospitalNames = response.data;\r\n    })\r\n  },\r\n  methods: {\r\n    canInitialFilter() {\r\n      return this.checkRole(['insurance', 'mz']);\r\n    },\r\n    handleCommand(command, index, row) {\r\n      switch (command) {\r\n        case 'handleUpdate':\r\n          this.handleUpdate(row);\r\n          break;\r\n        case 'handleDelete':\r\n          this.handleDelete(row);\r\n          break;\r\n        case 'handleCreatePdf':\r\n          this.handleCreatePdf(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      this.queryParams.status = this.getStatus();\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行查询\r\n      getWorkOrderPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    getStatus() {\r\n      return this.$route.query.status\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.detailOpen = false;\r\n      this.detailOpen2 = false;\r\n      this.esignOpen = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        name: undefined,\r\n        idCardNumber: undefined,\r\n        mobilePhoneNumber: undefined,\r\n        address: undefined,\r\n        hospitalName: undefined,\r\n        invoice: undefined,\r\n        bill: undefined,\r\n        medicalRecord: undefined,\r\n        summary: undefined,\r\n        diagnose: undefined,\r\n        disabilityReport: undefined,\r\n        deathProof: undefined,\r\n        adviceMoney: undefined,\r\n        suggestCompensatingMoney: undefined,\r\n        actualMoney: undefined,\r\n        compensatingDate: undefined,\r\n        remark: undefined,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRangeCompensatingDate = [];\r\n      this.dateRangeCreateTime = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工单\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getWorkOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工单\";\r\n      });\r\n    },\r\n    handleCreatePdf(row) {\r\n      createPdf(row.id).then(response => {\r\n        this.getList();\r\n      });\r\n    },\r\n    handleDetail(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"查看详情\";\r\n    },\r\n    handleDetail2(row) {\r\n      getSupplementaryFileRecord(row.supplementaryFileRecordId).then(response => {\r\n        this.detailForm = response.data;\r\n        try {\r\n          this.fileUrlMap = this.detailForm.fileUrls ? JSON.parse(this.detailForm.fileUrls) : {};\r\n          // 过滤掉没有图片的类型\r\n          this.fileUrlMap = Object.fromEntries(\r\n            Object.entries(this.fileUrlMap).filter(([_, urls]) => urls && urls.length > 0)\r\n          );\r\n\r\n          if (Object.keys(this.fileUrlMap).length > 0) {\r\n            // 设置第一个标签为激活状态\r\n            this.activeTab = Object.keys(this.fileUrlMap)[0];\r\n          }\r\n        } catch (e) {\r\n          console.error('解析 fileUrls 失败:', e);\r\n          this.fileUrlMap = {};\r\n        }\r\n        this.detailOpen2 = true;\r\n      });\r\n    },\r\n    doAction() {\r\n      let p = undefined;\r\n      switch(this.method) {\r\n        case 'take':\r\n          this.confirmBtnText = '正在签章,请稍候';\r\n          this.confirmBtnLoading = true;\r\n          p = takeWorkOrder(this.detailId);\r\n          break;\r\n        case 'hospital':\r\n          p = hospitalCheck(this.detailId);\r\n          break;\r\n        case 'process':\r\n          p = process2(this.detailId);\r\n          break;\r\n        case 'visit':\r\n          let submitData = this.$refs.workOrderDetail.getSubmitData()\r\n          submitData.id = this.detailId\r\n          p = visit(submitData);\r\n          break;\r\n        case 'reject':\r\n          let rejectData = {\r\n            id: this.detailId,\r\n            remark: this.$refs.workOrderDetail.getSubmitData().remark\r\n          }\r\n          p = reject(rejectData);\r\n          break;\r\n        case 'delay':\r\n          let delayData = {\r\n            id: this.detailId,\r\n            remark: this.$refs.workOrderDetail.getSubmitData().remark\r\n          }\r\n          p = delay(delayData);\r\n          break;\r\n        default:\r\n          console.log('找不到对应方法: ' + this.method);\r\n      }\r\n      p.then(() => {\r\n        this.confirmBtnLoading = false;\r\n        this.confirmBtnText = CONFIRM_TEXT;\r\n        this.cancel();\r\n        this.getList();\r\n      });\r\n    },\r\n    handleTake(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"接单\";\r\n      this.method = 'take';\r\n      this.confirmBtnText = '接 单';\r\n      this.opType = 'take';\r\n    },\r\n    handleReject(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"拒绝\";\r\n      this.method = 'reject';\r\n      this.confirmBtnText = '拒绝';\r\n      this.opType = 'reject';\r\n    },\r\n    handleDelay(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"延后\";\r\n      this.method = 'delay';\r\n      this.confirmBtnText = '延 后';\r\n      this.opType = 'delay';\r\n    },\r\n    handleHospitalCheck(row) {\r\n      this.detailId = row.id;\r\n      this.esignOpen = true;\r\n      this.method = 'hospital';\r\n    },\r\n    handleProcess(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"处理\";\r\n      this.method = 'process';\r\n      this.opType = 'process';\r\n\r\n    },\r\n    handleVisit(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"回访\";\r\n      this.method = 'visit';\r\n      this.opType = 'visit';\r\n    },\r\n    handleReturn(row) {\r\n      this.$modal.confirm(`是否确认回退工单(${row.name})`).then(function() {\r\n          return returnWorkOrder(row.id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"回退成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n        // 修改的提交\r\n        if (this.form.id != null) {\r\n          updateWorkOrder(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          });\r\n          return;\r\n        }\r\n        // 添加的提交\r\n        createWorkOrder(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\");\r\n          this.open = false;\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id;\r\n      this.$modal.confirm('是否确认删除工单编号为\"' + id + '\"的数据项?').then(function() {\r\n          return deleteWorkOrder(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有工单数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportWorkOrderExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '工单.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    handleEcard(row) {\r\n      this.ecardOpen = true;\r\n      this.ecard = {\r\n        idNum: row.idCardNumber,\r\n        name: row.name\r\n      }\r\n    },\r\n    handleOrder(row) {\r\n      this.orderOpen = true;\r\n      this.idNum = row.idCardNumber\r\n    },\r\n    handleDisablePerson(row) {\r\n      this.disablePerson.open = true;\r\n      this.disablePerson.idNum = row.idCardNumber;\r\n    },\r\n    handleRecord(row) {\r\n      window.open(row.pdf.signedPdf, \"_blank\", \"resizable,scrollbars,status\");\r\n    },\r\n    handleHouseHold(row) {\r\n      this.houseHoldOpen = true;\r\n      this.household = {\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    handleBankCard(row) {\r\n      this.bankAccount = {\r\n        bankAccountOpen: true,\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    handlerPersonInfo(row) {\r\n      this.personInfo = {\r\n        open: true,\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    checkPermi,\r\n    checkRole,\r\n    handleImport() {\r\n      this.upload.title = \"工单导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.url;\r\n    },\r\n    handleClaimAmountImport() {\r\n      this.upload.title = \"理赔金额导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.claimAmountUrl;\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      if (response.code !== 0) {\r\n        this.$modal.msgError(response.msg)\r\n        return;\r\n      }\r\n      let fileName = file.name;\r\n      let href = response.data;\r\n      let downA = document.createElement(\"a\");\r\n      downA.href = href;\r\n      downA.download = fileName;\r\n      downA.click();\r\n      window.URL.revokeObjectURL(href);\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$modal.msgSuccess(\"导入成功，请查看导入结果文件\");\r\n      this.getList();\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n    /** 处理补充资料按钮点击 */\r\n    handleSupplementary(row) {\r\n      // 先获取工单详情\r\n      getWorkOrder(row.id).then(response => {\r\n        const workOrder = response.data;\r\n        if (!workOrder.supplementaryFiles) {\r\n          this.$modal.msgError(\"没有补充资料\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          const files = JSON.parse(workOrder.supplementaryFiles);\r\n          // 过滤掉没有图片的类型\r\n          this.supplementary.files = Object.fromEntries(\r\n            Object.entries(files).filter(([_, urls]) => urls && urls.length > 0)\r\n          );\r\n\r\n          if (Object.keys(this.supplementary.files).length === 0) {\r\n            this.$modal.msgError(\"没有补充资料\");\r\n            return;\r\n          }\r\n\r\n          // 设置第一个标签为激活状态\r\n          this.supplementary.activeTab = Object.keys(this.supplementary.files)[0];\r\n          this.supplementary.open = true;\r\n        } catch (e) {\r\n          console.error(\"解析补充资料失败\", e);\r\n          this.$modal.msgError(\"解析补充资料失败\");\r\n        }\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"获取工单详情失败\");\r\n      });\r\n    },\r\n    /** 获取补充资料类型的显示文本 */\r\n    getSupplementaryTypeLabel(type) {\r\n      return this.supplementaryTypeMap[type] || type;\r\n    },\r\n    /** 万达数据导入按钮操作 */\r\n    handleWandaImport() {\r\n      this.upload.title = \"万达数据导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.wandaImportUrl;\r\n    },\r\n    /** 批量操作日志按钮操作 */\r\n    handleBatchOperationLog() {\r\n      this.batchLogDrawer.visible = true;\r\n      this.getBatchLogList();\r\n    },\r\n    /** 获取批量操作日志列表 */\r\n    getBatchLogList() {\r\n      this.batchLogDrawer.loading = true;\r\n      // 处理时间范围参数\r\n      let params = { ...this.batchLogDrawer.queryParams };\r\n      if (this.batchLogDrawer.dateRange && this.batchLogDrawer.dateRange.length === 2) {\r\n        params.beginStartTime = this.batchLogDrawer.dateRange[0];\r\n        params.endStartTime = this.batchLogDrawer.dateRange[1];\r\n      }\r\n\r\n      getBatchOperationLogPage(params).then(response => {\r\n        this.batchLogDrawer.list = response.data.list;\r\n        this.batchLogDrawer.total = response.data.total;\r\n        this.batchLogDrawer.loading = false;\r\n      }).catch(() => {\r\n        this.batchLogDrawer.loading = false;\r\n      });\r\n    },\r\n    /** 重置批量操作日志查询 */\r\n    resetBatchLogQuery() {\r\n      this.batchLogDrawer.dateRange = [];\r\n      this.batchLogDrawer.queryParams = {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        operationType: null,\r\n        status: null,\r\n        operatorName: null,\r\n        beginStartTime: null,\r\n        endStartTime: null\r\n      };\r\n      this.getBatchLogList();\r\n    },\r\n    /** 关闭批量操作日志抽屉 */\r\n    handleBatchLogDrawerClose() {\r\n      this.batchLogDrawer.visible = false;\r\n    },\r\n    /** 获取批量操作状态标签类型 */\r\n    getBatchLogStatusType(status) {\r\n      switch (status) {\r\n        case 'RUNNING':\r\n          return 'warning';\r\n        case 'COMPLETED':\r\n          return 'success';\r\n        case 'FAILED':\r\n          return 'danger';\r\n        default:\r\n          return 'info';\r\n      }\r\n    },\r\n    /** 格式化执行时长 */\r\n    formatDuration(duration) {\r\n      if (!duration) return '-';\r\n\r\n      const seconds = Math.floor(duration / 1000);\r\n      const minutes = Math.floor(seconds / 60);\r\n      const hours = Math.floor(minutes / 60);\r\n\r\n      if (hours > 0) {\r\n        return `${hours}小时${minutes % 60}分${seconds % 60}秒`;\r\n      } else if (minutes > 0) {\r\n        return `${minutes}分${seconds % 60}秒`;\r\n      } else {\r\n        return `${seconds}秒`;\r\n      }\r\n    },\r\n    /** 批量拒绝按钮操作 */\r\n    handleBatchReject() {\r\n      this.batchRejectDialog.visible = true;\r\n      this.batchRejectDialog.form.cutoffDate = null;\r\n    },\r\n    /** 关闭批量拒绝对话框 */\r\n    handleBatchRejectDialogClose() {\r\n      this.batchRejectDialog.visible = false;\r\n      this.batchRejectDialog.loading = false;\r\n      this.batchRejectDialog.form.cutoffDate = null;\r\n      // 清除表单验证\r\n      if (this.$refs.batchRejectForm) {\r\n        this.$refs.batchRejectForm.clearValidate();\r\n      }\r\n    },\r\n    /** 批量拒绝确认操作 */\r\n    handleBatchRejectConfirm() {\r\n      this.$refs.batchRejectForm.validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n\r\n        // 二次确认对话框\r\n        const cutoffDate = this.batchRejectDialog.form.cutoffDate;\r\n        const confirmMessage = `您确定要拒绝 ${cutoffDate} 及之前的所有待接单工单吗？此操作可通过日志恢复。`;\r\n\r\n        this.$modal.confirm(confirmMessage, '批量拒绝确认', {\r\n          confirmButtonText: '确定执行',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: false\r\n        }).then(() => {\r\n          this.executeBatchReject();\r\n        }).catch(() => {\r\n          // 用户取消操作\r\n        });\r\n      });\r\n    },\r\n    /** 执行批量拒绝操作 */\r\n    executeBatchReject() {\r\n      this.batchRejectDialog.loading = true;\r\n\r\n      // 将日期转换为时间戳（毫秒）\r\n      const cutoffDateStr = this.batchRejectDialog.form.cutoffDate + ' 23:59:59';\r\n      const cutoffDate = new Date(cutoffDateStr);\r\n      const cutoffTimestamp = cutoffDate.getTime();\r\n\r\n      const requestData = {\r\n        cutoffDate: cutoffTimestamp\r\n      };\r\n\r\n      batchRejectWorkOrders(requestData).then(response => {\r\n        this.batchRejectDialog.loading = false;\r\n        this.handleBatchRejectDialogClose();\r\n\r\n        // 显示成功提示\r\n        const { batchId, processedCount } = response.data;\r\n        this.$modal.msgSuccess(`操作成功！批次号：${batchId}，共处理了 ${processedCount} 条工单。`);\r\n\r\n        // 刷新列表\r\n        this.getList();\r\n      }).catch(error => {\r\n        this.batchRejectDialog.loading = false;\r\n        // 错误信息会由全局错误处理器显示\r\n        console.error('批量拒绝操作失败:', error);\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  .drawer-footer {\r\n    display: flex;\r\n    padding: 0 50px 20px;\r\n    .el-button {\r\n      flex: 1\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}