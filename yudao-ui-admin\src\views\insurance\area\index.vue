<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="区域名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入区域名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="保险公司" prop="companyId">
        <CompanySelect v-model="queryParams.companyId"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['insurance:area:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['insurance:area:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="区域名称" align="center" prop="name" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleViewCompany(scope.row)"
                     v-hasPermi="['insurance:area:query']">查看负责公司</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleSetCompany(scope.row)"
                     v-hasPermi="['insurance:area:update']">关联负责公司</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['insurance:area:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['insurance:area:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="区域名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入区域名称" />
        </el-form-item>
        <el-form-item label="子区域" prop="subArea">
          <el-select
            v-model="form.subArea"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择子区域">
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="负责公司列表" :visible.sync="areaCompany.listOpen" width="500px" append-to-body>
      <AreaCompanyList v-if="areaCompany.listOpen" :areaId="areaCompany.areaId" />
    </el-dialog>
    <el-dialog :title="areaCompany.chooseTitle" :visible.sync="areaCompany.createOpen" width="500px" append-to-body>
      <AreaCompanyChoose ref="companyChooser" :areaId="areaCompany.areaId" @submitSuccess="handleAreaCompanySubmitSuccess"/>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAreaCompanyForm">确 定</el-button>
        <el-button @click="cancelAreaCompanyForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createArea, updateArea, deleteArea, getArea, getAreaPage, exportAreaExcel } from "@/api/insurance/area";
import CompanySelect from "@/views/insurance/company/components/companySelect";
import AreaCompanyList from "./areaCompanyList";
import AreaCompanyChoose from "./areaCompanyChoose";
export default {
  name: "Area",
  components: {
    CompanySelect,
    AreaCompanyList,
    AreaCompanyChoose
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 区域列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        companyId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "区域名称不能为空", trigger: "blur" }],
      },
      areaCompany: {
        listOpen: false,
        createOpen: false,
        areaId: undefined,
        chooseTitle: undefined,
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 处理查询参数
      let params = {...this.queryParams};
      // 执行查询
      getAreaPage(params).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        companyId: undefined,
        subArea: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加区域";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getArea(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改区域";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateArea(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createArea(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除区域编号为"' + id + '"的数据项?').then(function() {
          return deleteArea(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      // 执行导出
      this.$modal.confirm('是否确认导出所有区域数据项?').then(() => {
          this.exportLoading = true;
          return exportAreaExcel(params);
        }).then(response => {
          this.$download.excel(response, '区域.xls');
          this.exportLoading = false;
        }).catch(() => {});
    },
    handleViewCompany(row) {
      this.areaCompany.listOpen = true;
      this.areaCompany.areaId = row.id;
    },
    handleSetCompany(row) {
      this.areaCompany.createOpen = true;
      this.areaCompany.areaId = row.id;
      this.areaCompany.chooseTitle = `关联负责公司:${row.name}`;
      this.$nextTick(()=>this.$refs.companyChooser.reset()) 
    },
    submitAreaCompanyForm() {
      this.$refs.companyChooser.submit();
    },
    cancelAreaCompanyForm() {
      this.areaCompany.createOpen = false;
      this.$refs.companyChooser.reset()
    },
    handleAreaCompanySubmitSuccess() {
      this.$modal.msgSuccess("关联负责公司成功");
      this.areaCompany.createOpen = false;
    }
  }
};
</script>
