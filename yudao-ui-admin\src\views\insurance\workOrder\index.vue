<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="保险类型">
        <el-select v-model="queryParams.type" placeholder="请选择保险类型" clearable>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INSURANCE_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value" v-if="(dict.value == '1' && checkPermi(['insurance:old-people-accident-insurance:query'])) || ((dict.value == '7' || dict.value == '8' || dict.value == '9') && checkPermi(['insurance:disabled-people-insurance:query']))" />
        </el-select>
      </el-form-item>
      <el-form-item label="医院名称">
        <el-select v-model="queryParams.hospitalName" placeholder="请选择医院" clearable>
          <el-option
            v-for="item in hospitalNames"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="保险公司">
        <CompanySelect v-model="queryParams.companyId" />
      </el-form-item>
      <el-form-item label="长者名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入长者名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="身份证号" prop="idCardNumber">
        <el-input v-model="queryParams.idCardNumber" placeholder="请输入身份证号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="联系方式" prop="mobilePhoneNumber">
        <el-input v-model="queryParams.mobilePhoneNumber" placeholder="请输入联系方式" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="理赔时间">
        <el-date-picker v-model="dateRangeCompensatingDate" style="width: 240px" value-format="yyyy-MM-dd"
                        type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item label="工单类型">
        <el-select v-model="queryParams.treatmentSerialNumberType" placeholder="请选择工单类型" clearable>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INSURANCE_TREATMENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="票据完整度">
        <el-select v-model="queryParams.completeStatus" placeholder="请选择完整度" clearable>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['insurance:work-order:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['insurance:work-order:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImport"
                   v-hasPermi="['insurance:work-order:settlement-import']">已理赔工单导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleClaimAmountImport"
                   v-hasPermi="['insurance:work-order:claim-import']">理赔金额导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" size="mini" icon="el-icon-upload2"
                   @click="handleWandaImport" :loading="wandaImportLoading"
                   v-hasPermi="['insurance:work-order:import']">万达医疗数据更新</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-document" size="mini"
                   @click="handleBatchOperationLog"
                   v-hasPermi="['insurance:work-order:query']">批量操作日志</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini"
                   @click="handleBatchReject"
                   v-hasPermi="['insurance:work-order:batch-reject']">批量拒绝旧工单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="编号" align="center" type="index" width="50px"/>
      <el-table-column label="保险类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INSURANCE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="工单类型" align="center" prop="treatmentSerialNumberType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INSURANCE_TREATMENT_TYPE" :value="scope.row.treatmentSerialNumberType" />
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermi(['insurance:work-order:show-company'])" label="负责公司" align="center" prop="companyName" />
      <el-table-column label="长者名称" align="center" prop="desensitizedName" />
      <el-table-column label="身份证号" align="center" prop="desensitizedIdCardNumber" />
      <el-table-column label="联系方式" align="center" prop="desensitizedMobilePhoneNumber" />
      <el-table-column label="医院" align="center" prop="hospitalName" />
      <el-table-column label="就诊时间" align="center" prop="treatmentDatetime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.treatmentDatetime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="建议理赔金额" align="center" width="100" prop="suggestCompensatingMoney" />
      <el-table-column label="赔付金额" align="center" prop="actualMoney" />
      <el-table-column label="票据完整度" align="center" prop="completeStatus">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS" :value="scope.row.completeStatus" />
        </template>
      </el-table-column>
      <el-table-column label="险种" align="center" prop="completeStatus">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INSURANCE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="授权状态" align="center" prop="authStatus" />
      <el-table-column label="理赔申请" align="center" prop="supplementaryFileRecordId">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.supplementaryFileRecordId == null">否</el-tag>
          <el-tag v-else type="info">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width" width="300px">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEcard(scope.row)"
                     v-if="checkPermi(['insurance:elec:cert'])">电子证照</el-button>
          <el-button size="mini" type="text" @click="handleOrder(scope.row)"
                     v-if="checkPermi(['insurance:elec:ticket'])">电子保单</el-button>
          <el-button size="mini" type="text" @click="handleHouseHold(scope.row)"
                     v-if="checkPermi(['insurance:elec:house'])">电子户口</el-button>
          <el-button size="mini" type="text" @click="handleBankCard(scope.row)"
                     v-if="checkPermi(['insurance:elec:bankcard'])">银行账号</el-button>
          <el-button size="mini" type="text" @click="handlerPersonInfo(scope.row)"
                     v-if="checkPermi(['insurance:work-order:query'])">联系方式</el-button>
          <el-button size="mini" type="text" @click="handleDisablePerson(scope.row)"
                     v-if="checkPermi(['insurance:work-order:query'])">残疾人证</el-button>
          <el-button size="mini" type="text" @click="handleRecord(scope.row)"
                     v-if="checkPermi(['insurance:work-order:medical']) && scope.row.status > 1">就诊证明</el-button>
          <el-button size="mini" type="text" @click="if(!scope.row.supplementaryFileRecordId) { handleDetail(scope.row); } else { handleDetail2(scope.row); }"
                    v-if="checkPermi(['insurance:work-order:query'])">详情</el-button>
          <el-button size="mini" type="text" @click="handleTake(scope.row)"
                    v-if="checkPermi(['insurance:work-order:take']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))">接单</el-button>
          <el-button size="mini" type="text" @click="handleReject(scope.row)"
                    v-if="checkPermi(['insurance:work-order:reject']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))">拒绝</el-button>
          <el-button size="mini" type="text" @click="handleDelay(scope.row)"
                    v-if="checkPermi(['insurance:work-order:delay']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))">延后</el-button>
          <el-button size="mini" type="text" @click="handleHospitalCheck(scope.row)"
                    v-if="checkPermi(['insurance:work-order:hospital-check']) && scope.row.status === 1 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))">盖章</el-button>
          <el-button size="mini" type="text" @click="handleProcess(scope.row)"
                    v-if="checkPermi(['insurance:work-order:process']) && scope.row.status === 2 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))">处理</el-button>
          <el-button size="mini" type="text" @click="handleVisit(scope.row)"
                    v-if="checkPermi(['insurance:work-order:visit']) && scope.row.status === 3 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))">回访</el-button>
          <el-button size="mini" type="text" @click="handleReturn(scope.row)"
                    v-if="checkPermi(['insurance:work-order:return']) && (scope.row.status === 2 || scope.row.status === 3 || scope.row.status === 6) && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))">回退</el-button>
          <el-button size="mini" type="text" @click="handleSupplementary(scope.row)"
                     v-if="checkPermi(['insurance:work-order:query'])">补充资料</el-button>
          <el-dropdown  @command="(command) => handleCommand(command, scope.$index, scope.row)"
                        v-if="queryParams.status != null"
                        v-hasPermi="['insurance:work-order:update', 'insurance:work-order:delete']">
                <span class="el-dropdown-link">
                  <i class="el-icon-d-arrow-right el-icon--right"></i>更多
                </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="handleDelete" size="mini" type="text" icon="el-icon-delete"
                                v-hasPermi="['insurance:work-order:delete']">删除</el-dropdown-item>
              <el-dropdown-item command="handleCreatePdf" size="mini" type="text" icon="el-icon-edit"
                                v-hasPermi="['insurance:work-order:update']">创建未签章pdf</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-drawer :title="detailTitle" :visible.sync="detailOpen" direction="rtl" size="60%">
      <WorkOrderDetail ref="workOrderDetail" :opType="opType" v-if="detailOpen" :id="detailId" />
      <div class="drawer-footer" v-if="detailTitle !== '查看详情'">
        <el-button type="primary" @click="doAction" :loading="confirmBtnLoading">{{confirmBtnText}}</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>
    <el-drawer title="电子签章" :visible.sync="esignOpen" direction="rtl" size="90%">
      <esign />
      <div class="drawer-footer">
        <el-button type="primary" @click="doAction">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>
    <el-dialog title="电子证照" :visible.sync="ecardOpen" width="500px">
      <ecard :idNum="ecard.idNum" :name="ecard.name" />
    </el-dialog>
    <el-drawer title="保单详情" :visible.sync="orderOpen" direction="rtl" size="90%">
      <order :idNum="idNum"/>
    </el-drawer>
    <el-drawer title="残疾人证" :visible.sync="disablePerson.open" direction="rtl" size="90%">
      <disablePerson :idNum="disablePerson.idNum"/>
    </el-drawer>
    <el-dialog title="电子户口" :visible.sync="houseHoldOpen" width="550px">
      <household :idNum="household.idNum" />
    </el-dialog>
    <el-dialog title="银行账号" :visible.sync="bankAccount.bankAccountOpen" width="550px">
      <bankCard :idCard="bankAccount.idNum" />
    </el-dialog>
    <el-dialog title="多来源信息" :visible.sync="personInfo.open" width="550px">
      <personInfo :idCard="personInfo.idNum" />
    </el-dialog>

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="补充资料" :visible.sync="supplementary.open" width="800px" append-to-body>
      <el-tabs v-model="supplementary.activeTab">
        <el-tab-pane v-for="(urls, type) in supplementary.files"
                     :key="type"
                     :label="getSupplementaryTypeLabel(type)"
                     :name="type">
          <el-row :gutter="10">
            <el-col :span="8" v-for="(url, index) in urls" :key="index">
              <el-image
                style="width: 100%; height: 200px"
                :src="url"
                :preview-src-list="urls">
              </el-image>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog :title="'补充资料详情'" :visible.sync="detailOpen2" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="居民">{{ detailForm.residentName }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :type="DICT_TYPE.SUPPLEMENTARY_RECORD_STATUS" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="户籍地址">{{ detailForm.hjAddress }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ detailForm.phone }}</el-descriptions-item>
        <el-descriptions-item label="实际理赔金额" v-if="detailForm.actualMoney">
          {{ detailForm.actualMoney }}元
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">补充资料</el-divider>
      <el-tabs v-model="activeTab">
        <el-tab-pane
          v-for="(urls, type) in fileUrlMap"
          :key="type"
          :label="getSupplementaryTypeLabel(type)"
          :name="type">
          <el-row :gutter="10">
            <el-col :span="8" v-for="(url, index) in urls" :key="index">
              <el-image
                style="width: 100%; height: 200px"
                :src="url"
                :preview-src-list="urls">
              </el-image>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 批量操作日志抽屉 -->
    <el-drawer
      title="批量操作日志"
      :visible.sync="batchLogDrawer.visible"
      direction="rtl"
      size="60%"
      :before-close="handleBatchLogDrawerClose">

      <!-- 搜索条件 -->
      <el-form :model="batchLogDrawer.queryParams" ref="batchLogQueryForm" size="small" :inline="true" label-width="100px" style="margin-bottom: 20px;">
        <el-form-item label="操作类型">
          <el-select v-model="batchLogDrawer.queryParams.operationType" placeholder="请选择操作类型" clearable>
            <el-option label="批量拒绝" value="BATCH_REJECT"></el-option>
            <el-option label="批量恢复" value="BATCH_RECOVER"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作状态">
          <el-select v-model="batchLogDrawer.queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="执行中" value="RUNNING"></el-option>
            <el-option label="已完成" value="COMPLETED"></el-option>
            <el-option label="执行失败" value="FAILED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作员">
          <el-input v-model="batchLogDrawer.queryParams.operatorName" placeholder="请输入操作员姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="batchLogDrawer.dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getBatchLogList">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetBatchLogQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 批量操作日志表格 -->
      <el-table v-loading="batchLogDrawer.loading" :data="batchLogDrawer.list" style="width: 100%">
        <el-table-column label="批次号" align="center" prop="batchId" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作类型" align="center" prop="operationTypeDisplay" width="100"></el-table-column>
        <el-table-column label="状态" align="center" prop="statusDisplay" width="80">
          <template slot-scope="scope">
            <el-tag :type="getBatchLogStatusType(scope.row.status)">{{ scope.row.statusDisplay }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="处理数量" align="center" prop="processedCount" width="80"></el-table-column>
        <el-table-column label="操作员" align="center" prop="operatorName" width="100"></el-table-column>
        <el-table-column label="开始时间" align="center" prop="startTime" width="150">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="endTime" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.endTime ? parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="执行时长" align="center" width="100">
          <template slot-scope="scope">
            <span>{{ formatDuration(scope.row.duration) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remarks" show-overflow-tooltip></el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="batchLogDrawer.total > 0"
        :total="batchLogDrawer.total"
        :page.sync="batchLogDrawer.queryParams.pageNo"
        :limit.sync="batchLogDrawer.queryParams.pageSize"
        @pagination="getBatchLogList"
        style="margin-top: 20px;" />
    </el-drawer>

    <!-- 批量拒绝模态框 -->
    <el-dialog
      title="批量拒绝历史待接单工单"
      :visible.sync="batchRejectDialog.visible"
      width="500px"
      :before-close="handleBatchRejectDialogClose">

      <div style="margin-bottom: 20px;">
        <p style="color: #606266; line-height: 1.6;">
          此操作将批量拒绝指定日期及之前的所有待接单工单。被拒绝的工单状态将变更为"行政拒绝"，
          此操作可通过批量操作日志进行恢复。请谨慎操作。
        </p>
      </div>

      <el-form :model="batchRejectDialog.form" ref="batchRejectForm" label-width="120px">
        <el-form-item label="截止日期" prop="cutoffDate"
                      :rules="[{ required: true, message: '请选择截止日期', trigger: 'change' }]">
          <el-date-picker
            v-model="batchRejectDialog.form.cutoffDate"
            type="date"
            placeholder="选择截止日期"
            value-format="yyyy-MM-dd"
            style="width: 100%">
          </el-date-picker>
          <div style="font-size: 12px; color: #909399; margin-top: 5px;">
            将拒绝此日期及之前的所有待接单工单
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleBatchRejectDialogClose">取 消</el-button>
        <el-button type="danger" @click="handleBatchRejectConfirm" :loading="batchRejectDialog.loading">执 行</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createWorkOrder,
  updateWorkOrder,
  deleteWorkOrder,
  getWorkOrder,
  getWorkOrderPage,
  exportWorkOrderExcel,
  takeWorkOrder,
  hospitalCheck,
  process as process2,
  visit,
  reject,
  delay,
  returnWorkOrder,
  getHospitalNames,
  createPdf,
  getBatchOperationLogPage,
  batchRejectWorkOrders
}
from "@/api/insurance/workOrder";
import {getSupplementaryFileRecord} from "@/api/insurance/supplementaryFileRecord";
import ImageUpload from '@/components/ImageUpload';
import { checkPermi, checkRole } from "@/utils/permission";
import WorkOrderDetail from "./detail"
import ecard from '../components/ecard.vue';
import esign from '../components/esignature.vue';
import order from '../components/order.vue';
import disablePerson from '../components/disablePerson.vue';
import household from '../components/household.vue';
import bankCard from '../components/bankCard.vue';
import personInfo from '../components/personInfo.vue';
import CompanySelect from '../company/components/companySelect.vue';
import {getBaseHeader} from "@/utils/request";
const CONFIRM_TEXT = '确 定';
export default {
  name: "WorkOrder",
  components: {
    ImageUpload,
    WorkOrderDetail,
    ecard,
    esign,
    order,
    household,
    bankCard,
    personInfo,
    disablePerson,
    CompanySelect,
  },
  data() {

    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      dateRangeCompensatingDate: [],
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        type: null,
        name: null,
        idCardNumber: null,
        mobilePhoneNumber: null,
        treatmentSerialNumberType: this.canInitialFilter()? '1': null,
        completeStatus: this.canInitialFilter()? '0': null,
        hospitalName: null,
        companyId: null,
        types: [1, 7, 8, 9],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "长者名称不能为空", trigger: "blur" }],
        idCardNumber: [{ required: true, message: "身份证号不能为空", trigger: "blur" }],
      },
      detailId: undefined,
      detailTitle: undefined,
      detailOpen: false,
      detailOpen2: false,
      method: undefined,
      ecardOpen: false,
      esignOpen: false,
      orderOpen: false,
      disablePerson: {
        open: false,
        idNum: undefined,
      },
      ecard: {
        idNum: undefined,
        name: undefined
      },
      confirmBtnText: CONFIRM_TEXT,
      confirmBtnLoading: false,
      houseHoldOpen: false,
      household: {
        idNum: undefined,
      },
      opType: undefined,
      idNum: undefined,
      bankAccount: {
        bankAccountOpen: false,
        idNum: undefined
      },
      personInfo: {
        open: false,
        idNum: undefined
      },
      hospitalNames: [],
      //已赔付工单导入
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-settlement-work-order',
        claimAmountUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-claim-amount-work-order',
        wandaImportUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-wanda-data'
      },
      supplementary: {
        open: false,
        activeTab: '',
        files: {},
      },
      supplementaryTypeMap: {
        MEDICAL_DIAGNOSIS_PROOF: '医疗诊断证明',
        MEDICAL_FEE_INVOICE: '医疗费用发票',
        DRUG_LIST: '用药清单',
        MEDICAL_SETTLEMENT: '医保结算单',
        OUTPATIENT_MEDICAL_RECORDS: '门诊病历(门诊)',
        DISCHARGE_RECORD: '出院记录(住院)',
        DISABILITY_CERTIFICATE: '残疾鉴定报告',
        TRAFFIC_ACCIDENT_CERTIFICATE: '交通事故责任认定书'
      },
      wandaImportLoading: false,
      detailForm: {},
      fileUrlMap: {},
      activeTab: '',
      // 批量操作日志抽屉
      batchLogDrawer: {
        visible: false,
        loading: false,
        total: 0,
        list: [],
        dateRange: [],
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          operationType: null,
          status: null,
          operatorName: null,
          beginStartTime: null,
          endStartTime: null
        }
      },
      // 批量拒绝对话框
      batchRejectDialog: {
        visible: false,
        loading: false,
        form: {
          cutoffDate: null
        }
      }
    };
  },
  created() {
    this.getList();
    getHospitalNames().then(response => {
      this.hospitalNames = response.data;
    })
  },
  methods: {
    canInitialFilter() {
      return this.checkRole(['insurance', 'mz']);
    },
    handleCommand(command, index, row) {
      switch (command) {
        case 'handleUpdate':
          this.handleUpdate(row);
          break;
        case 'handleDelete':
          this.handleDelete(row);
          break;
        case 'handleCreatePdf':
          this.handleCreatePdf(row);
          break;
        default:
          break;
      }
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 处理查询参数
      this.queryParams.status = this.getStatus();
      let params = {...this.queryParams};
      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');
      // 执行查询
      getWorkOrderPage(params).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getStatus() {
      return this.$route.query.status
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.detailOpen = false;
      this.detailOpen2 = false;
      this.esignOpen = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        idCardNumber: undefined,
        mobilePhoneNumber: undefined,
        address: undefined,
        hospitalName: undefined,
        invoice: undefined,
        bill: undefined,
        medicalRecord: undefined,
        summary: undefined,
        diagnose: undefined,
        disabilityReport: undefined,
        deathProof: undefined,
        adviceMoney: undefined,
        suggestCompensatingMoney: undefined,
        actualMoney: undefined,
        compensatingDate: undefined,
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCompensatingDate = [];
      this.dateRangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getWorkOrder(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工单";
      });
    },
    handleCreatePdf(row) {
      createPdf(row.id).then(response => {
        this.getList();
      });
    },
    handleDetail(row) {
      this.detailId = row.id;
      this.detailOpen = true;
      this.detailTitle = "查看详情";
    },
    handleDetail2(row) {
      getSupplementaryFileRecord(row.supplementaryFileRecordId).then(response => {
        this.detailForm = response.data;
        try {
          this.fileUrlMap = this.detailForm.fileUrls ? JSON.parse(this.detailForm.fileUrls) : {};
          // 过滤掉没有图片的类型
          this.fileUrlMap = Object.fromEntries(
            Object.entries(this.fileUrlMap).filter(([_, urls]) => urls && urls.length > 0)
          );

          if (Object.keys(this.fileUrlMap).length > 0) {
            // 设置第一个标签为激活状态
            this.activeTab = Object.keys(this.fileUrlMap)[0];
          }
        } catch (e) {
          console.error('解析 fileUrls 失败:', e);
          this.fileUrlMap = {};
        }
        this.detailOpen2 = true;
      });
    },
    doAction() {
      let p = undefined;
      switch(this.method) {
        case 'take':
          this.confirmBtnText = '正在签章,请稍候';
          this.confirmBtnLoading = true;
          p = takeWorkOrder(this.detailId);
          break;
        case 'hospital':
          p = hospitalCheck(this.detailId);
          break;
        case 'process':
          p = process2(this.detailId);
          break;
        case 'visit':
          let submitData = this.$refs.workOrderDetail.getSubmitData()
          submitData.id = this.detailId
          p = visit(submitData);
          break;
        case 'reject':
          let rejectData = {
            id: this.detailId,
            remark: this.$refs.workOrderDetail.getSubmitData().remark
          }
          p = reject(rejectData);
          break;
        case 'delay':
          let delayData = {
            id: this.detailId,
            remark: this.$refs.workOrderDetail.getSubmitData().remark
          }
          p = delay(delayData);
          break;
        default:
          console.log('找不到对应方法: ' + this.method);
      }
      p.then(() => {
        this.confirmBtnLoading = false;
        this.confirmBtnText = CONFIRM_TEXT;
        this.cancel();
        this.getList();
      });
    },
    handleTake(row) {
      this.detailId = row.id;
      this.detailOpen = true;
      this.detailTitle = "接单";
      this.method = 'take';
      this.confirmBtnText = '接 单';
      this.opType = 'take';
    },
    handleReject(row) {
      this.detailId = row.id;
      this.detailOpen = true;
      this.detailTitle = "拒绝";
      this.method = 'reject';
      this.confirmBtnText = '拒绝';
      this.opType = 'reject';
    },
    handleDelay(row) {
      this.detailId = row.id;
      this.detailOpen = true;
      this.detailTitle = "延后";
      this.method = 'delay';
      this.confirmBtnText = '延 后';
      this.opType = 'delay';
    },
    handleHospitalCheck(row) {
      this.detailId = row.id;
      this.esignOpen = true;
      this.method = 'hospital';
    },
    handleProcess(row) {
      this.detailId = row.id;
      this.detailOpen = true;
      this.detailTitle = "处理";
      this.method = 'process';
      this.opType = 'process';

    },
    handleVisit(row) {
      this.detailId = row.id;
      this.detailOpen = true;
      this.detailTitle = "回访";
      this.method = 'visit';
      this.opType = 'visit';
    },
    handleReturn(row) {
      this.$modal.confirm(`是否确认回退工单(${row.name})`).then(function() {
          return returnWorkOrder(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("回退成功");
        }).catch(() => {});
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateWorkOrder(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createWorkOrder(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除工单编号为"' + id + '"的数据项?').then(function() {
          return deleteWorkOrder(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');
      // 执行导出
      this.$modal.confirm('是否确认导出所有工单数据项?').then(() => {
          this.exportLoading = true;
          return exportWorkOrderExcel(params);
        }).then(response => {
          this.$download.excel(response, '工单.xls');
          this.exportLoading = false;
        }).catch(() => {});
    },
    handleEcard(row) {
      this.ecardOpen = true;
      this.ecard = {
        idNum: row.idCardNumber,
        name: row.name
      }
    },
    handleOrder(row) {
      this.orderOpen = true;
      this.idNum = row.idCardNumber
    },
    handleDisablePerson(row) {
      this.disablePerson.open = true;
      this.disablePerson.idNum = row.idCardNumber;
    },
    handleRecord(row) {
      window.open(row.pdf.signedPdf, "_blank", "resizable,scrollbars,status");
    },
    handleHouseHold(row) {
      this.houseHoldOpen = true;
      this.household = {
        idNum: row.idCardNumber,
      }
    },
    handleBankCard(row) {
      this.bankAccount = {
        bankAccountOpen: true,
        idNum: row.idCardNumber,
      }
    },
    handlerPersonInfo(row) {
      this.personInfo = {
        open: true,
        idNum: row.idCardNumber,
      }
    },
    checkPermi,
    checkRole,
    handleImport() {
      this.upload.title = "工单导入";
      this.upload.open = true;
      this.upload.url = this.upload.url;
    },
    handleClaimAmountImport() {
      this.upload.title = "理赔金额导入";
      this.upload.open = true;
      this.upload.url = this.upload.claimAmountUrl;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return;
      }
      let fileName = file.name;
      let href = response.data;
      let downA = document.createElement("a");
      downA.href = href;
      downA.download = fileName;
      downA.click();
      window.URL.revokeObjectURL(href);
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$modal.msgSuccess("导入成功，请查看导入结果文件");
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 处理补充资料按钮点击 */
    handleSupplementary(row) {
      // 先获取工单详情
      getWorkOrder(row.id).then(response => {
        const workOrder = response.data;
        if (!workOrder.supplementaryFiles) {
          this.$modal.msgError("没有补充资料");
          return;
        }

        try {
          const files = JSON.parse(workOrder.supplementaryFiles);
          // 过滤掉没有图片的类型
          this.supplementary.files = Object.fromEntries(
            Object.entries(files).filter(([_, urls]) => urls && urls.length > 0)
          );

          if (Object.keys(this.supplementary.files).length === 0) {
            this.$modal.msgError("没有补充资料");
            return;
          }

          // 设置第一个标签为激活状态
          this.supplementary.activeTab = Object.keys(this.supplementary.files)[0];
          this.supplementary.open = true;
        } catch (e) {
          console.error("解析补充资料失败", e);
          this.$modal.msgError("解析补充资料失败");
        }
      }).catch(() => {
        this.$modal.msgError("获取工单详情失败");
      });
    },
    /** 获取补充资料类型的显示文本 */
    getSupplementaryTypeLabel(type) {
      return this.supplementaryTypeMap[type] || type;
    },
    /** 万达数据导入按钮操作 */
    handleWandaImport() {
      this.upload.title = "万达数据导入";
      this.upload.open = true;
      this.upload.url = this.upload.wandaImportUrl;
    },
    /** 批量操作日志按钮操作 */
    handleBatchOperationLog() {
      this.batchLogDrawer.visible = true;
      this.getBatchLogList();
    },
    /** 获取批量操作日志列表 */
    getBatchLogList() {
      this.batchLogDrawer.loading = true;
      // 处理时间范围参数
      let params = { ...this.batchLogDrawer.queryParams };
      if (this.batchLogDrawer.dateRange && this.batchLogDrawer.dateRange.length === 2) {
        params.beginStartTime = this.batchLogDrawer.dateRange[0];
        params.endStartTime = this.batchLogDrawer.dateRange[1];
      }

      getBatchOperationLogPage(params).then(response => {
        this.batchLogDrawer.list = response.data.list;
        this.batchLogDrawer.total = response.data.total;
        this.batchLogDrawer.loading = false;
      }).catch(() => {
        this.batchLogDrawer.loading = false;
      });
    },
    /** 重置批量操作日志查询 */
    resetBatchLogQuery() {
      this.batchLogDrawer.dateRange = [];
      this.batchLogDrawer.queryParams = {
        pageNo: 1,
        pageSize: 10,
        operationType: null,
        status: null,
        operatorName: null,
        beginStartTime: null,
        endStartTime: null
      };
      this.getBatchLogList();
    },
    /** 关闭批量操作日志抽屉 */
    handleBatchLogDrawerClose() {
      this.batchLogDrawer.visible = false;
    },
    /** 获取批量操作状态标签类型 */
    getBatchLogStatusType(status) {
      switch (status) {
        case 'RUNNING':
          return 'warning';
        case 'COMPLETED':
          return 'success';
        case 'FAILED':
          return 'danger';
        default:
          return 'info';
      }
    },
    /** 格式化执行时长 */
    formatDuration(duration) {
      if (!duration) return '-';

      const seconds = Math.floor(duration / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);

      if (hours > 0) {
        return `${hours}小时${minutes % 60}分${seconds % 60}秒`;
      } else if (minutes > 0) {
        return `${minutes}分${seconds % 60}秒`;
      } else {
        return `${seconds}秒`;
      }
    },
    /** 批量拒绝按钮操作 */
    handleBatchReject() {
      this.batchRejectDialog.visible = true;
      this.batchRejectDialog.form.cutoffDate = null;
    },
    /** 关闭批量拒绝对话框 */
    handleBatchRejectDialogClose() {
      this.batchRejectDialog.visible = false;
      this.batchRejectDialog.loading = false;
      this.batchRejectDialog.form.cutoffDate = null;
      // 清除表单验证
      if (this.$refs.batchRejectForm) {
        this.$refs.batchRejectForm.clearValidate();
      }
    },
    /** 批量拒绝确认操作 */
    handleBatchRejectConfirm() {
      this.$refs.batchRejectForm.validate(valid => {
        if (!valid) {
          return;
        }

        // 二次确认对话框
        const cutoffDate = this.batchRejectDialog.form.cutoffDate;
        const confirmMessage = `您确定要拒绝 ${cutoffDate} 及之前的所有待接单工单吗？此操作可通过日志恢复。`;

        this.$modal.confirm(confirmMessage, '批量拒绝确认', {
          confirmButtonText: '确定执行',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }).then(() => {
          this.executeBatchReject();
        }).catch(() => {
          // 用户取消操作
        });
      });
    },
    /** 执行批量拒绝操作 */
    executeBatchReject() {
      this.batchRejectDialog.loading = true;

      // 将日期转换为时间戳（毫秒）
      const cutoffDateStr = this.batchRejectDialog.form.cutoffDate + ' 23:59:59';
      const cutoffDate = new Date(cutoffDateStr);
      const cutoffTimestamp = cutoffDate.getTime();

      const requestData = {
        cutoffDate: cutoffTimestamp
      };

      batchRejectWorkOrders(requestData).then(response => {
        this.batchRejectDialog.loading = false;
        this.handleBatchRejectDialogClose();

        // 显示成功提示
        const { batchId, processedCount } = response.data;
        this.$modal.msgSuccess(`操作成功！批次号：${batchId}，共处理了 ${processedCount} 条工单。`);

        // 刷新列表
        this.getList();
      }).catch(error => {
        this.batchRejectDialog.loading = false;
        // 错误信息会由全局错误处理器显示
        console.error('批量拒绝操作失败:', error);
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  .drawer-footer {
    display: flex;
    padding: 0 50px 20px;
    .el-button {
      flex: 1
    }
  }
}
</style>
